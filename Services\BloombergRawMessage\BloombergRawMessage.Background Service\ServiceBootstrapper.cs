using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;
using Siepe.Shared.DBUtility;
using Siepe.Shared.DBUtility.v1;
using Siepe.GenevaUtility;
using Siepe.PubSubUtility;
using Siepe.Serialization;
using Siepe.Serialization.JsonSerializer;
using Siepe.Instruments.Data;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.IssuerCreation;
using System;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker
{
    public static class ServiceBootstrapper
    {
        public static IServiceCollection AddBloombergRawMessageServices(this IServiceCollection services, IConfiguration configuration)
        {
            try
            {
                // Database Access
                services.AddScoped<ISqlDbAccess>(provider =>
                    new Siepe.Shared.DBUtility.SqlDbAccess(configuration.GetConnectionString("CoreConnectionString")));

                services.AddScoped<IDbAccess>(provider =>
                    new Siepe.Shared.DBUtility.v1.SqlDbAccess(configuration.GetConnectionString("CoreConnectionString")));

                // Geneva Services
                services.AddScoped<IGenevaDataProvider>(provider =>
                    new GenevaDataProvider(
                        configuration.GetConnectionString("FeedsConnectionString") ?? configuration.GetConnectionString("CoreConnectionString"),
                        provider.GetRequiredService<ISqlDbAccess>()));

                services.AddScoped<IGenevaServiceProvider, GenevaServiceProvider>();

                // Publication and Serialization
                services.AddScoped<IPublicationService, PublicationService>();
                services.AddScoped<IIssuerCreator>(provider =>
                    new IssuerCreator(provider.GetRequiredService<ISqlDbAccess>()));
                services.AddScoped<ISerializer, JsonSerializer>();

                // Transaction Type Resolution
                services.AddScoped<ITransactionResolver, AggregateTransactionResolver>();

                // Register all ITransactionType implementations
                RegisterTransactionTypes(services);

                // Instrument Type Resolution
                services.AddScoped<IInstTypeResolver, AggregateInstTypeResolver>();

                // Register all IInstrumentTypeHandler implementations
                RegisterInstrumentTypeHandlers(services);

                // Instrument Providers
                services.AddScoped<IInstrumentDataProvider>(provider =>
                    new InstrumentDataProvider(provider.GetRequiredService<IDbAccess>()));

                // Trade Feed Services
                services.AddScoped<ITradeFeedConfigurationProvider>(provider =>
                    new TradeFeedConfigurationProvider(provider.GetRequiredService<ISqlDbAccess>()));

                services.AddScoped<IAllocationProvider, AllocationProvider>();
                services.AddScoped<ITradeFeedHandler, TradeFeedHandler>();

                // Fee Services
                services.AddScoped<IFeeDataProvider, FeeDataProvider>();
                services.AddScoped<ICalculationEngine, CalculationEngine>();
                // Note: FeeHandler registration removed as it's not needed for this service scope

                // Rules Engine Service (required by CalculationEngine)
                services.AddScoped<Siepe.RulesEngine.Services.IRulesEngineSvc, Siepe.RulesEngine.Services.RulesEngineSvc>();

                // Raw Trade Processors
                services.AddScoped<IRawTradeProcessorProvider, RawTradeProcessorProvider>();
                services.AddScoped<IRawTradeProcessor, CfdSwapProcessor>(provider =>
                    new CfdSwapProcessor(provider.GetRequiredService<ISqlDbAccess>()));

                // Register array for RawTradeProcessorProvider
                services.AddScoped<IRawTradeProcessor[]>(provider =>
                    provider.GetServices<IRawTradeProcessor>().ToArray());

                // Main Handlers
                services.AddScoped<IRawMessageHandler, RawMessageHandler>();
                services.AddScoped<MessageProcessor>();

                return services;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error configuring Bloomberg Raw Message services: {ex.Message}", ex);
            }
        }

        private static void RegisterTransactionTypes(IServiceCollection services)
        {
            // Register specific transaction type implementations
            services.AddScoped<ITransactionType, BuyNewTransaction>();
            services.AddScoped<ITransactionType, SellNewTransaction>();
            services.AddScoped<ITransactionType, SellShortNewTransaction>();
            services.AddScoped<ITransactionType, CoverShortNewTransaction>();
            // Add other transaction types as needed

            // Register array for AggregateTransactionResolver
            services.AddScoped<ITransactionType[]>(provider =>
                provider.GetServices<ITransactionType>().ToArray());
        }

        private static void RegisterInstrumentTypeHandlers(IServiceCollection services)
        {
            // Register specific instrument type handlers
            services.AddScoped<IInstrumentTypeHandler, FutureHandler>();
            services.AddScoped<IInstrumentTypeHandler, EquityHandler>();
            services.AddScoped<IInstrumentTypeHandler, BondHandler>();
            services.AddScoped<IInstrumentTypeHandler, OptionHandler>();
            // Add other instrument type handlers as needed

            // Register array for AggregateInstTypeResolver
            services.AddScoped<IInstrumentTypeHandler[]>(provider =>
                provider.GetServices<IInstrumentTypeHandler>().ToArray());
        }
    }
}
